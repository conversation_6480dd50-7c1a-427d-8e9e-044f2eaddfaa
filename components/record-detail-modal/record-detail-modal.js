// components/record-detail-modal/record-detail-modal.js
// 记录详情弹窗组件 - 优化版

Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 是否显示弹窗
    visible: {
      type: Boolean,
      value: false
    },
    // 记录数据
    record: {
      type: Object,
      value: {}
    },
    // 是否允许点击遮罩关闭
    maskClosable: {
      type: Boolean,
      value: true
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 获取类型文本
     */
    getTypeText(type) {
      const typeMap = {
        'health': '健康记录',
        'production': '生产记录',
        'finance': '财务记录',
        'entry': '入栏记录',
        'weight': '称重记录',
        'sale': '出栏记录',
        'vaccination': '防疫记录',
        'treatment': '治疗记录',
        'death': '死亡记录',
        'sick': '生病记录'
      };
      return typeMap[type] || '记录';
    },

    /**
     * 获取状态文本
     */
    getStatusText(status) {
      const statusMap = {
        'healthy': '健康',
        'sick': '生病',
        'treated': '已治疗',
        'vaccination': '已防疫',
        'entry': '已入栏',
        'death': '死亡',
        'normal': '正常',
        'abnormal': '异常'
      };
      return statusMap[status] || status;
    },

    /**
     * 获取详情数据项
     */
    getDetailItems(details) {
      if (!details || typeof details !== 'object') {
        return [];
      }

      const items = [];
      const labelMap = {
        'count': { label: '数量', unit: '只' },
        'weight': { label: '重量', unit: 'kg' },
        'price': { label: '单价', unit: '元' },
        'cost': { label: '成本', unit: '元' },
        'totalIncome': { label: '总收入', unit: '元' },
        'source': { label: '来源', unit: '' },
        'temperature': { label: '温度', unit: '°C' },
        'humidity': { label: '湿度', unit: '%' },
        'feedConsumption': { label: '饲料消耗', unit: 'kg' },
        'eggCount': { label: '产蛋数量', unit: '个' },
        'mortality': { label: '死亡数量', unit: '只' },
        'medicine': { label: '药物', unit: '' },
        'dosage': { label: '剂量', unit: 'ml' },
        'symptoms': { label: '症状', unit: '' },
        'diagnosis': { label: '诊断', unit: '' },
        'treatment': { label: '治疗方案', unit: '' }
      };

      for (const [key, value] of Object.entries(details)) {
        if (value !== null && value !== undefined && value !== '') {
          const config = labelMap[key] || { label: key, unit: '' };
          items.push({
            key,
            label: config.label,
            value: value,
            unit: config.unit
          });
        }
      }

      return items;
    },

    /**
     * 阻止事件冒泡
     */
    stopPropagation() {
      // 阻止事件冒泡，防止点击内容区域关闭弹窗
    },

    /**
     * 点击遮罩
     */
    onMaskTap() {
      if (this.properties.maskClosable) {
        this.onClose();
      }
    },

    /**
     * 关闭弹窗
     */
    onClose() {
      this.triggerEvent('close');
    },

    /**
     * 编辑记录
     */
    onEdit() {
      this.triggerEvent('edit', {
        record: this.properties.record
      });
    },

    /**
     * 查看关联记录
     */
    onViewRelated() {
      this.triggerEvent('viewRelated', {
        relatedRecord: this.properties.record.relatedRecord
      });
    },

    /**
     * 预览图片
     */
    onPreviewImage(e) {
      const index = e.currentTarget.dataset.index;
      const images = this.properties.record.images || [];
      
      if (images.length === 0) return;

      const urls = images.map(img => img.url || img);
      
      wx.previewImage({
        current: urls[index],
        urls: urls
      });
    }
  },

  /**
   * 组件生命周期
   */
  lifetimes: {
    attached() {
      // 组件实例被放入页面节点树后执行
    },

    detached() {
      // 组件实例被从页面节点树移除后执行
    }
  },

  /**
   * 组件所在页面的生命周期
   */
  pageLifetimes: {
    show() {
      // 组件所在的页面被展示时执行
    },

    hide() {
      // 组件所在的页面被隐藏时执行
    }
  }
});
