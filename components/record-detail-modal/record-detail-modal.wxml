<!-- components/record-detail-modal/record-detail-modal.wxml -->
<!-- 记录详情弹窗组件 - 优化版 -->

<view class="record-detail-modal" wx:if="{{visible}}" catchtap="onMaskTap">
  <!-- 遮罩层 -->
  <view class="modal-mask"></view>
  
  <!-- 弹窗内容 -->
  <view class="modal-container" catchtap="stopPropagation">
    <!-- 头部 -->
    <view class="modal-header">
      <view class="header-content">
        <view class="modal-title">{{record.title || '记录详情'}}</view>
        <view class="record-type-badge type-{{record.type || 'default'}}">
          {{getTypeText(record.type)}}
        </view>
      </view>
      <view class="close-btn" bindtap="onClose">
        <text class="close-icon">×</text>
      </view>
    </view>

    <!-- 内容区域 -->
    <scroll-view class="modal-body" scroll-y="true">
      <!-- 基本信息组 -->
      <view class="info-group">
        <view class="group-title">
          <text class="title-icon">📋</text>
          <text class="title-text">基本信息</text>
        </view>
        <view class="info-grid">
          <view class="info-item" wx:if="{{record.date}}">
            <text class="info-label">记录日期</text>
            <text class="info-value">{{record.date}}</text>
          </view>
          <view class="info-item" wx:if="{{record.author}}">
            <text class="info-label">记录人</text>
            <text class="info-value">{{record.author}}</text>
          </view>
          <view class="info-item" wx:if="{{record.status}}">
            <text class="info-label">状态</text>
            <view class="status-badge status-{{record.status}}">
              {{getStatusText(record.status)}}
            </view>
          </view>
          <view class="info-item" wx:if="{{record.batch}}">
            <text class="info-label">批次</text>
            <text class="info-value">{{record.batch}}</text>
          </view>
        </view>
      </view>

      <!-- 详细内容组 -->
      <view class="info-group" wx:if="{{record.description || record.summary}}">
        <view class="group-title">
          <text class="title-icon">📝</text>
          <text class="title-text">详细描述</text>
        </view>
        <view class="content-text">
          {{record.description || record.summary}}
        </view>
      </view>

      <!-- 数据信息组 -->
      <view class="info-group" wx:if="{{record.details}}">
        <view class="group-title">
          <text class="title-icon">📊</text>
          <text class="title-text">数据信息</text>
        </view>
        <view class="data-grid">
          <block wx:for="{{getDetailItems(record.details)}}" wx:key="key">
            <view class="data-item">
              <text class="data-label">{{item.label}}</text>
              <text class="data-value">{{item.value}}</text>
              <text class="data-unit" wx:if="{{item.unit}}">{{item.unit}}</text>
            </view>
          </block>
        </view>
      </view>

      <!-- 图片信息组 -->
      <view class="info-group" wx:if="{{record.images && record.images.length > 0}}">
        <view class="group-title">
          <text class="title-icon">🖼️</text>
          <text class="title-text">相关图片</text>
        </view>
        <view class="images-grid">
          <block wx:for="{{record.images}}" wx:key="index">
            <image 
              class="image-item" 
              src="{{item.url || item}}" 
              mode="aspectFill"
              bindtap="onPreviewImage"
              data-index="{{index}}"
            ></image>
          </block>
        </view>
      </view>

      <!-- 关联信息组 -->
      <view class="info-group" wx:if="{{record.relatedRecord}}">
        <view class="group-title">
          <text class="title-icon">🔗</text>
          <text class="title-text">关联记录</text>
        </view>
        <view class="related-item" bindtap="onViewRelated">
          <view class="related-content">
            <text class="related-type">{{record.relatedRecord.type}}</text>
            <text class="related-desc">{{record.relatedRecord.description}}</text>
          </view>
          <text class="related-arrow">›</text>
        </view>
      </view>
    </scroll-view>

    <!-- 底部操作区 -->
    <view class="modal-footer">
      <button class="footer-btn btn-secondary" bindtap="onClose">关闭</button>
      <button 
        class="footer-btn btn-primary" 
        bindtap="onEdit" 
        wx:if="{{record.canEdit !== false}}"
      >
        编辑
      </button>
    </view>
  </view>
</view>
