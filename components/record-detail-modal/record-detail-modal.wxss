/* components/record-detail-modal/record-detail-modal.wxss */
/* 记录详情弹窗组件样式 - 优化版 */

/* CSS变量定义 */
:host {
  --primary-color: #0066cc;
  --primary-light: #e6f3ff;
  --success-color: #52c41a;
  --success-light: #f6ffed;
  --warning-color: #fa8c16;
  --warning-light: #fff7e6;
  --error-color: #ff4d4f;
  --error-light: #fff2f0;
  --text-primary: #262626;
  --text-secondary: #595959;
  --text-tertiary: #8c8c8c;
  --bg-container: #ffffff;
  --bg-layout: #f5f6f8;
  --border-light: #f0f0f0;
  --border-medium: #d9d9d9;
  --shadow-sm: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
  --shadow-md: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
  --shadow-lg: 0 8rpx 24rpx rgba(0, 0, 0, 0.12);
  --shadow-xl: 0 12rpx 32rpx rgba(0, 0, 0, 0.15);
  --radius-sm: 8rpx;
  --radius-md: 12rpx;
  --radius-lg: 16rpx;
  --radius-xl: 20rpx;
  --radius-2xl: 24rpx;
  --space-xs: 8rpx;
  --space-sm: 12rpx;
  --space-md: 16rpx;
  --space-lg: 24rpx;
  --space-xl: 32rpx;
  --space-2xl: 40rpx;
  --space-3xl: 48rpx;
  --transition-fast: 0.2s ease;
  --transition-normal: 0.3s ease;
  --transition-slow: 0.5s ease;
}

/* 弹窗容器 */
.record-detail-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: modal-fade-in var(--transition-normal) ease;
}

@keyframes modal-fade-in {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* 遮罩层 */
.modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4rpx);
}

/* 弹窗主容器 */
.modal-container {
  position: relative;
  width: 90%;
  max-width: 700rpx;
  max-height: 85vh;
  background: var(--bg-container);
  border-radius: var(--radius-2xl);
  box-shadow: var(--shadow-xl);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  animation: modal-slide-up var(--transition-normal) ease;
}

@keyframes modal-slide-up {
  from {
    opacity: 0;
    transform: translateY(50rpx) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* 头部样式 */
.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-xl) var(--space-xl) var(--space-lg);
  background: linear-gradient(135deg, var(--primary-light) 0%, rgba(240, 255, 244, 0.3) 100%);
  border-bottom: 1rpx solid var(--border-light);
  position: relative;
}

.modal-header::before {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 2rpx;
  background: linear-gradient(90deg, var(--primary-color) 0%, var(--success-color) 100%);
}

.header-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: var(--space-sm);
}

.modal-title {
  font-size: 36rpx;
  font-weight: 600;
  color: var(--text-primary);
  letter-spacing: 0.5rpx;
  line-height: 1.3;
}

.record-type-badge {
  font-size: 22rpx;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  font-weight: 500;
  align-self: flex-start;
  letter-spacing: 0.5rpx;
}

.type-health {
  background: var(--success-light);
  color: var(--success-color);
  border: 1rpx solid rgba(82, 196, 26, 0.2);
}

.type-production {
  background: var(--primary-light);
  color: var(--primary-color);
  border: 1rpx solid rgba(0, 102, 204, 0.2);
}

.type-finance {
  background: var(--warning-light);
  color: var(--warning-color);
  border: 1rpx solid rgba(250, 140, 22, 0.2);
}

.type-default {
  background: var(--bg-layout);
  color: var(--text-secondary);
  border: 1rpx solid var(--border-medium);
}

.close-btn {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: rgba(0, 0, 0, 0.05);
  transition: var(--transition-fast);
  margin-left: var(--space-md);
}

.close-btn:active {
  background: rgba(0, 0, 0, 0.1);
  transform: scale(0.95);
}

.close-icon {
  font-size: 40rpx;
  color: var(--text-tertiary);
  font-weight: 300;
  line-height: 1;
}

/* 内容区域 */
.modal-body {
  flex: 1;
  padding: var(--space-lg) var(--space-xl);
  overflow-y: auto;
}

/* 信息组样式 */
.info-group {
  margin-bottom: var(--space-2xl);
}

.info-group:last-child {
  margin-bottom: 0;
}

.group-title {
  display: flex;
  align-items: center;
  margin-bottom: var(--space-lg);
  padding-bottom: var(--space-sm);
  border-bottom: 2rpx solid var(--border-light);
}

.title-icon {
  font-size: 32rpx;
  margin-right: var(--space-sm);
}

.title-text {
  font-size: 30rpx;
  font-weight: 600;
  color: var(--text-primary);
  letter-spacing: 0.5rpx;
}

/* 信息网格 */
.info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-lg);
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: var(--space-xs);
}

.info-label {
  font-size: 24rpx;
  color: var(--text-tertiary);
  font-weight: 500;
}

.info-value {
  font-size: 28rpx;
  color: var(--text-primary);
  font-weight: 500;
}

/* 状态标签 */
.status-badge {
  font-size: 22rpx;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  font-weight: 500;
  align-self: flex-start;
}

.status-healthy {
  background: var(--success-light);
  color: var(--success-color);
}

.status-sick {
  background: var(--error-light);
  color: var(--error-color);
}

.status-treated {
  background: var(--warning-light);
  color: var(--warning-color);
}

/* 内容文本 */
.content-text {
  font-size: 28rpx;
  color: var(--text-secondary);
  line-height: 1.6;
  padding: var(--space-lg);
  background: var(--bg-layout);
  border-radius: var(--radius-lg);
  border: 1rpx solid var(--border-light);
}

/* 数据网格 */
.data-grid {
  display: flex;
  flex-direction: column;
  gap: var(--space-md);
}

.data-item {
  display: flex;
  align-items: center;
  padding: var(--space-md) var(--space-lg);
  background: var(--bg-layout);
  border-radius: var(--radius-md);
  border: 1rpx solid var(--border-light);
}

.data-label {
  font-size: 26rpx;
  color: var(--text-tertiary);
  min-width: 120rpx;
  margin-right: var(--space-md);
}

.data-value {
  font-size: 28rpx;
  color: var(--text-primary);
  font-weight: 500;
  flex: 1;
}

.data-unit {
  font-size: 24rpx;
  color: var(--text-tertiary);
  margin-left: var(--space-xs);
}

/* 图片网格 */
.images-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--space-md);
}

.image-item {
  width: 100%;
  height: 160rpx;
  border-radius: var(--radius-md);
  border: 1rpx solid var(--border-light);
  transition: var(--transition-fast);
}

.image-item:active {
  transform: scale(0.98);
}

/* 关联项 */
.related-item {
  display: flex;
  align-items: center;
  padding: var(--space-lg);
  background: var(--bg-layout);
  border-radius: var(--radius-lg);
  border: 1rpx solid var(--border-light);
  transition: var(--transition-fast);
}

.related-item:active {
  background: var(--primary-light);
  border-color: var(--primary-color);
}

.related-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: var(--space-xs);
}

.related-type {
  font-size: 26rpx;
  color: var(--primary-color);
  font-weight: 500;
}

.related-desc {
  font-size: 24rpx;
  color: var(--text-tertiary);
}

.related-arrow {
  font-size: 32rpx;
  color: var(--text-tertiary);
  font-weight: 300;
}

/* 底部操作区 */
.modal-footer {
  display: flex;
  gap: var(--space-md);
  padding: var(--space-lg) var(--space-xl);
  padding-bottom: calc(var(--space-lg) + env(safe-area-inset-bottom));
  border-top: 1rpx solid var(--border-light);
  background: var(--bg-container);
}

.footer-btn {
  flex: 1;
  height: 80rpx;
  border-radius: var(--radius-md);
  font-size: 28rpx;
  font-weight: 500;
  border: none;
  transition: var(--transition-fast);
}

.btn-secondary {
  background: var(--bg-layout);
  color: var(--text-secondary);
  border: 1rpx solid var(--border-medium);
}

.btn-secondary:active {
  background: var(--border-light);
}

.btn-primary {
  background: linear-gradient(135deg, var(--primary-color) 0%, #4096ff 100%);
  color: #ffffff;
  box-shadow: var(--shadow-sm);
}

.btn-primary:active {
  transform: translateY(-1rpx);
  box-shadow: var(--shadow-md);
}

/* 响应式设计 */
@media screen and (max-width: 750rpx) {
  .modal-container {
    width: 95%;
    max-height: 90vh;
  }
  
  .modal-header {
    padding: var(--space-lg) var(--space-lg) var(--space-md);
  }
  
  .modal-title {
    font-size: 32rpx;
  }
  
  .modal-body {
    padding: var(--space-md) var(--space-lg);
  }
  
  .info-grid {
    grid-template-columns: 1fr;
    gap: var(--space-md);
  }
  
  .images-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .modal-footer {
    padding: var(--space-md) var(--space-lg);
    padding-bottom: calc(var(--space-md) + env(safe-area-inset-bottom));
  }
}
