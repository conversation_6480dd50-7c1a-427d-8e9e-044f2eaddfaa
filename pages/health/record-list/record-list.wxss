/* pages/health/record-list/record-list.wxss */
/* 优化版记录列表样式 - V3.0 */

/* CSS变量定义 */
page {
  --primary-color: #0066cc;
  --primary-light: #e6f3ff;
  --success-color: #52c41a;
  --success-light: #f6ffed;
  --warning-color: #fa8c16;
  --warning-light: #fff7e6;
  --error-color: #ff4d4f;
  --error-light: #fff2f0;
  --text-primary: #262626;
  --text-secondary: #595959;
  --text-tertiary: #8c8c8c;
  --bg-container: #ffffff;
  --bg-layout: #f5f6f8;
  --border-light: #f0f0f0;
  --border-medium: #d9d9d9;
  --shadow-sm: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
  --shadow-md: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
  --shadow-lg: 0 8rpx 24rpx rgba(0, 0, 0, 0.12);
  --radius-sm: 8rpx;
  --radius-md: 12rpx;
  --radius-lg: 16rpx;
  --radius-xl: 20rpx;
  --space-xs: 8rpx;
  --space-sm: 12rpx;
  --space-md: 16rpx;
  --space-lg: 24rpx;
  --space-xl: 32rpx;
  --transition-fast: 0.2s ease;
  --transition-normal: 0.3s ease;
}

.record-list-container {
  min-height: 100vh;
  background: linear-gradient(180deg, var(--bg-layout) 0%, #fafbfc 100%);
  padding: var(--space-lg);
  box-sizing: border-box;
}

/* 筛选栏优化 */
.filter-bar {
  display: flex;
  background: var(--bg-container);
  border-radius: var(--radius-xl);
  padding: var(--space-sm);
  margin-bottom: var(--space-lg);
  box-shadow: var(--shadow-sm);
  border: 1rpx solid var(--border-light);
  backdrop-filter: blur(10rpx);
}

.filter-item {
  flex: 1;
  text-align: center;
  padding: var(--space-md) var(--space-sm);
  font-size: 28rpx;
  color: var(--text-secondary);
  border-radius: var(--radius-md);
  transition: var(--transition-normal);
  position: relative;
  font-weight: 500;
}

.filter-item.active {
  background: linear-gradient(135deg, var(--primary-color) 0%, #4096ff 100%);
  color: #ffffff;
  font-weight: 600;
  box-shadow: 0 2rpx 8rpx rgba(0, 102, 204, 0.3);
  transform: translateY(-1rpx);
}

.filter-item:not(.active):active {
  background-color: var(--primary-light);
  color: var(--primary-color);
}

/* 记录容器 */
.records-container {
  padding-bottom: 140rpx;
}

/* 记录项优化 */
.record-item {
  background: var(--bg-container);
  border-radius: var(--radius-xl);
  padding: 0;
  margin-bottom: var(--space-lg);
  box-shadow: var(--shadow-sm);
  border: 1rpx solid var(--border-light);
  overflow: hidden;
  transition: var(--transition-normal);
  position: relative;
}

.record-item::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4rpx;
  background: linear-gradient(180deg, var(--primary-color) 0%, var(--success-color) 100%);
  transition: var(--transition-normal);
}

.record-item:active {
  transform: translateY(-2rpx);
  box-shadow: var(--shadow-md);
}

.record-item:active::before {
  width: 6rpx;
}

/* 记录头部重新设计 */
.record-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: var(--space-lg) var(--space-xl) var(--space-md);
  background: linear-gradient(135deg, rgba(0, 102, 204, 0.02) 0%, rgba(82, 196, 26, 0.02) 100%);
}

.record-title {
  font-size: 32rpx;
  font-weight: 600;
  color: var(--text-primary);
  flex: 1;
  margin-right: var(--space-md);
  line-height: 1.4;
  letter-spacing: 0.5rpx;
}

.record-status {
  font-size: 22rpx;
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  white-space: nowrap;
  font-weight: 500;
  letter-spacing: 0.5rpx;
  position: relative;
  overflow: hidden;
}

/* 状态标签优化 */
.status-healthy {
  background: linear-gradient(135deg, var(--success-light) 0%, #f0f9e8 100%);
  color: var(--success-color);
  border: 1rpx solid rgba(82, 196, 26, 0.2);
}

.status-healthy::before {
  content: '●';
  margin-right: 4rpx;
  color: var(--success-color);
}

.status-sick {
  background: linear-gradient(135deg, var(--error-light) 0%, #fff1f0 100%);
  color: var(--error-color);
  border: 1rpx solid rgba(255, 77, 79, 0.2);
}

.status-sick::before {
  content: '⚠';
  margin-right: 4rpx;
  color: var(--error-color);
}

.status-treated {
  background: linear-gradient(135deg, var(--warning-light) 0%, #fef9e6 100%);
  color: var(--warning-color);
  border: 1rpx solid rgba(250, 140, 22, 0.2);
}

.status-treated::before {
  content: '✓';
  margin-right: 4rpx;
  color: var(--warning-color);
}

/* 记录元信息重新设计 */
.record-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 var(--space-xl) var(--space-md);
  background: rgba(0, 0, 0, 0.02);
  margin: 0;
}

.record-date {
  font-size: 24rpx;
  color: var(--text-tertiary);
  font-weight: 500;
  display: flex;
  align-items: center;
}

.record-date::before {
  content: '📅';
  margin-right: 6rpx;
  font-size: 20rpx;
}

.record-author {
  font-size: 24rpx;
  color: var(--text-tertiary);
  font-weight: 500;
  display: flex;
  align-items: center;
}

.record-author::before {
  content: '👤';
  margin-right: 6rpx;
  font-size: 20rpx;
}

/* 记录摘要优化 */
.record-summary {
  font-size: 28rpx;
  color: var(--text-secondary);
  line-height: 1.6;
  padding: var(--space-md) var(--space-xl) var(--space-lg);
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-word;
  position: relative;
}

.record-summary::after {
  content: '';
  position: absolute;
  bottom: 0;
  right: 0;
  width: 60rpx;
  height: 40rpx;
  background: linear-gradient(90deg, transparent 0%, var(--bg-container) 70%);
  pointer-events: none;
}

/* 空状态优化 */
.empty-state {
  text-align: center;
  padding: var(--space-3xl) var(--space-xl);
  background: var(--bg-container);
  border-radius: var(--radius-xl);
  margin: var(--space-lg) 0;
  border: 2rpx dashed var(--border-medium);
  position: relative;
  overflow: hidden;
}

.empty-state::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4rpx;
  background: linear-gradient(90deg, var(--primary-color) 0%, var(--success-color) 100%);
}

.empty-icon {
  width: 160rpx;
  height: 160rpx;
  margin-bottom: var(--space-lg);
  opacity: 0.6;
  filter: grayscale(20%);
  transition: var(--transition-normal);
}

.empty-state:active .empty-icon {
  transform: scale(1.05);
  opacity: 0.8;
}

.empty-text {
  display: block;
  font-size: 30rpx;
  color: var(--text-secondary);
  margin-bottom: var(--space-xl);
  font-weight: 500;
  letter-spacing: 0.5rpx;
}

.empty-btn {
  background: linear-gradient(135deg, var(--primary-color) 0%, #4096ff 100%);
  color: #ffffff;
  border: none;
  width: 240rpx;
  height: 80rpx;
  line-height: 80rpx;
  border-radius: var(--radius-md);
  font-size: 28rpx;
  font-weight: 500;
  box-shadow: var(--shadow-md);
  transition: var(--transition-normal);
}

.empty-btn:active {
  transform: translateY(-2rpx);
  box-shadow: var(--shadow-lg);
}

/* 加载状态优化 */
.load-more,
.no-more {
  text-align: center;
  padding: var(--space-xl) 0;
  font-size: 26rpx;
  color: var(--text-tertiary);
  font-weight: 500;
  position: relative;
}

.load-more::before {
  content: '⏳';
  margin-right: var(--space-xs);
  animation: rotate 2s linear infinite;
}

.no-more::before {
  content: '✨';
  margin-right: var(--space-xs);
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

/* 添加按钮优化 */
.add-button {
  position: fixed;
  bottom: 60rpx;
  right: 60rpx;
  width: 120rpx;
  height: 120rpx;
  background: linear-gradient(135deg, var(--primary-color) 0%, #4096ff 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: var(--shadow-lg);
  transition: var(--transition-normal);
  z-index: 100;
}

.add-button:active {
  transform: scale(0.95) translateY(-4rpx);
  box-shadow: 0 8rpx 32rpx rgba(0, 102, 204, 0.4);
}

.add-button image {
  width: 60rpx;
  height: 60rpx;
  filter: brightness(0) invert(1);
}

/* 响应式设计 */
@media screen and (max-width: 750rpx) {
  .record-list-container {
    padding: var(--space-md);
  }

  .record-item {
    margin-bottom: var(--space-md);
  }

  .record-header {
    padding: var(--space-md) var(--space-lg) var(--space-sm);
  }

  .record-title {
    font-size: 30rpx;
  }

  .record-summary {
    padding: var(--space-sm) var(--space-lg) var(--space-md);
    font-size: 26rpx;
  }

  .add-button {
    width: 100rpx;
    height: 100rpx;
    bottom: 40rpx;
    right: 40rpx;
  }

  .add-button image {
    width: 50rpx;
    height: 50rpx;
  }
}